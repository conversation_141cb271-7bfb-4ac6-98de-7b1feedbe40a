# 串口2输出数组功能说明

## 功能概述

本功能将串口2输出的内容原封不动地保存在3个数组中，每次新的输出都会按顺序覆盖之前的数组内容。

## 实现细节

### 1. 数据结构
- 使用3个 `std::string` 数组来保存串口2输出
- 使用循环索引来管理数组覆盖顺序
- 每次输出都会完整保存，包括格式化的文本和结束符

### 2. 核心功能

#### 保存串口2输出
在 `Application::AddSttResult()` 函数中，每次串口2输出时都会：
1. 将完整的输出内容保存到当前数组位置
2. 更新数组索引（循环0-2）
3. 记录调试日志

#### 获取保存的数据
提供了以下公共接口：
```cpp
// 获取数组指针
const std::string* GetUartOutputArrays() const;

// 获取当前数组索引
int GetCurrentUartArrayIndex() const;

// 获取数组大小
static constexpr size_t GetUartOutputArraySize();

// 打印所有数组内容（调试用）
void PrintUartOutputArrays() const;
```

## 使用示例

### 1. 获取所有保存的串口2输出
```cpp
auto& app = Application::GetInstance();
const std::string* arrays = app.GetUartOutputArrays();
size_t array_size = app.GetUartOutputArraySize();

for (size_t i = 0; i < array_size; ++i) {
    if (!arrays[i].empty()) {
        ESP_LOGI("UART2_ARRAY", "Array[%zu]: %s", i, arrays[i].c_str());
    }
}
```

### 2. 获取最新的串口2输出
```cpp
auto& app = Application::GetInstance();
int current_index = app.GetCurrentUartArrayIndex();
const std::string* arrays = app.GetUartOutputArrays();

// 获取最新保存的输出（注意：current_index指向下一个要写入的位置）
int latest_index = (current_index - 1 + app.GetUartOutputArraySize()) % app.GetUartOutputArraySize();
if (!arrays[latest_index].empty()) {
    ESP_LOGI("UART2_LATEST", "Latest: %s", arrays[latest_index].c_str());
}
```

### 3. 调试输出
```cpp
auto& app = Application::GetInstance();
app.PrintUartOutputArrays(); // 打印所有数组内容
```

## 输出格式

串口2输出的格式为：
```
t{index}.txt="{text}"\xff\xff\xff
```

其中：
- `{index}` 是3-5的循环索引
- `{text}` 是语音识别的文本内容
- `\xff\xff\xff` 是结束符

## 注意事项

1. **线程安全**：所有操作都在 `mutex_` 保护下进行
2. **内存管理**：使用 `std::string` 自动管理内存
3. **循环覆盖**：数组索引在0-2之间循环，确保只保留最新的3条输出
4. **完整保存**：保存的内容包括格式化字符串和结束符，与原串口输出完全一致

## 调试功能

- 每次保存都会输出调试日志
- 每5次输出后会自动打印所有数组内容
- 可以通过 `PrintUartOutputArrays()` 手动查看数组状态 