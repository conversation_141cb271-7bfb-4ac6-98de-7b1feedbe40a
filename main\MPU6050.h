#ifndef MPU6050_H
#define MPU6050_H

#include <driver/i2c_master.h>
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

// MPU6050 I2C地址
#define MPU6050_ADDR 0x68

// MPU6050寄存器地址
#define MPU6050_REG_PWR_MGMT_1    0x6B
#define MPU6050_REG_SMPLRT_DIV    0x19
#define MPU6050_REG_CONFIG        0x1A
#define MPU6050_REG_GYRO_CONFIG   0x1B
#define MPU6050_REG_ACCEL_CONFIG  0x1C
#define MPU6050_REG_ACCEL_XOUT_H  0x3B
#define MPU6050_REG_ACCEL_XOUT_L  0x3C
#define MPU6050_REG_ACCEL_YOUT_H  0x3D
#define MPU6050_REG_ACCEL_YOUT_L  0x3E
#define MPU6050_REG_ACCEL_ZOUT_H  0x3F
#define MPU6050_REG_ACCEL_ZOUT_L  0x40
#define MPU6050_REG_TEMP_OUT_H    0x41
#define MPU6050_REG_TEMP_OUT_L    0x42
#define MPU6050_REG_GYRO_XOUT_H   0x43
#define MPU6050_REG_GYRO_XOUT_L   0x44
#define MPU6050_REG_GYRO_YOUT_H   0x45
#define MPU6050_REG_GYRO_YOUT_L   0x46
#define MPU6050_REG_GYRO_ZOUT_H   0x47
#define MPU6050_REG_GYRO_ZOUT_L   0x48
#define MPU6050_REG_WHO_AM_I      0x75

// 数据结构
typedef struct {
    float accel_x;
    float accel_y;
    float accel_z;
    float gyro_x;
    float gyro_y;
    float gyro_z;
    float temperature;
} mpu6050_data_t;

class MPU6050 {
public:
    MPU6050(i2c_master_bus_handle_t i2c_bus);
    ~MPU6050();

    // 初始化MPU6050
    esp_err_t init();

    // 读取所有传感器数据
    esp_err_t read_data(mpu6050_data_t* data);

    // 读取加速度计数据
    esp_err_t read_accel(float* accel_x, float* accel_y, float* accel_z);

    // 读取陀螺仪数据
    esp_err_t read_gyro(float* gyro_x, float* gyro_y, float* gyro_z);

    // 读取温度数据
    esp_err_t read_temperature(float* temperature);

    // 检查设备是否连接
    bool is_connected();

private:
    i2c_master_dev_handle_t i2c_device_;
    bool initialized_;

    // I2C读写函数
    esp_err_t write_reg(uint8_t reg, uint8_t value);
    esp_err_t read_reg(uint8_t reg, uint8_t* value);
    esp_err_t read_regs(uint8_t reg, uint8_t* buffer, size_t length);
};

#endif // MPU6050_H