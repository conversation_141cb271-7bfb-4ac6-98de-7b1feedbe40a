#include "MPU6050.h"
#include <esp_log.h>
#include <math.h>

#define TAG "MPU6050"

// 加速度计和陀螺仪的量程设置
#define ACCEL_SCALE_FACTOR 16384.0f  // ±2g
#define GYRO_SCALE_FACTOR  131.0f    // ±250°/s

MPU6050::MPU6050(i2c_master_bus_handle_t i2c_bus) : initialized_(false) {
    // 配置I2C设备
    i2c_device_config_t i2c_device_cfg = {
        .dev_addr_length = I2C_ADDR_BIT_LEN_7,
        .device_address = MPU6050_ADDR,
        .scl_speed_hz = 400000,  // 400kHz
        .scl_wait_us = 0,
        .flags = {
            .disable_ack_check = 0,
        },
    };

    esp_err_t ret = i2c_master_bus_add_device(i2c_bus, &i2c_device_cfg, &i2c_device_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to add I2C device: %s", esp_err_to_name(ret));
        i2c_device_ = nullptr;
    }
}

MPU6050::~MPU6050() {
    if (i2c_device_) {
        i2c_master_bus_rm_device(i2c_device_);
    }
}

esp_err_t MPU6050::init() {
    if (!i2c_device_) {
        ESP_LOGE(TAG, "I2C device not initialized");
        return ESP_FAIL;
    }

    esp_err_t ret;

    // 检查设备ID
    uint8_t who_am_i;
    ret = read_reg(MPU6050_REG_WHO_AM_I, &who_am_i);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read WHO_AM_I register: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "WHO_AM_I: 0x%02X", who_am_i);
    if (who_am_i != 0x68 && who_am_i != 0x72) {  // 0x72 is also valid for some MPU6050 variants
        ESP_LOGW(TAG, "Unexpected WHO_AM_I value: 0x%02X (expected 0x68 or 0x72)", who_am_i);
        // 继续初始化，可能是兼容的设备
    }

    // 复位设备
    ret = write_reg(MPU6050_REG_PWR_MGMT_1, 0x80);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to reset device");
        return ret;
    }

    vTaskDelay(pdMS_TO_TICKS(100));  // 等待复位完成

    // 唤醒设备（退出睡眠模式）
    ret = write_reg(MPU6050_REG_PWR_MGMT_1, 0x00);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to wake up device");
        return ret;
    }

    // 设置采样率分频器 (1kHz / (1 + 7) = 125Hz)
    ret = write_reg(MPU6050_REG_SMPLRT_DIV, 0x07);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set sample rate");
        return ret;
    }

    // 配置数字低通滤波器
    ret = write_reg(MPU6050_REG_CONFIG, 0x06);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure filter");
        return ret;
    }

    // 配置陀螺仪量程 (±250°/s)
    ret = write_reg(MPU6050_REG_GYRO_CONFIG, 0x00);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure gyroscope");
        return ret;
    }

    // 配置加速度计量程 (±2g)
    ret = write_reg(MPU6050_REG_ACCEL_CONFIG, 0x00);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure accelerometer");
        return ret;
    }

    initialized_ = true;
    ESP_LOGI(TAG, "MPU6050 initialized successfully");
    return ESP_OK;
}

esp_err_t MPU6050::read_data(mpu6050_data_t* data) {
    if (!initialized_ || !data) {
        return ESP_FAIL;
    }

    uint8_t raw_data[14];
    esp_err_t ret = read_regs(MPU6050_REG_ACCEL_XOUT_H, raw_data, 14);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read sensor data");
        return ret;
    }

    // 解析加速度计数据
    int16_t accel_x_raw = (int16_t)((raw_data[0] << 8) | raw_data[1]);
    int16_t accel_y_raw = (int16_t)((raw_data[2] << 8) | raw_data[3]);
    int16_t accel_z_raw = (int16_t)((raw_data[4] << 8) | raw_data[5]);

    // 解析温度数据
    int16_t temp_raw = (int16_t)((raw_data[6] << 8) | raw_data[7]);

    // 解析陀螺仪数据
    int16_t gyro_x_raw = (int16_t)((raw_data[8] << 8) | raw_data[9]);
    int16_t gyro_y_raw = (int16_t)((raw_data[10] << 8) | raw_data[11]);
    int16_t gyro_z_raw = (int16_t)((raw_data[12] << 8) | raw_data[13]);

    // 转换为实际物理值
    data->accel_x = (float)accel_x_raw / ACCEL_SCALE_FACTOR;
    data->accel_y = (float)accel_y_raw / ACCEL_SCALE_FACTOR;
    data->accel_z = (float)accel_z_raw / ACCEL_SCALE_FACTOR;

    data->gyro_x = (float)gyro_x_raw / GYRO_SCALE_FACTOR;
    data->gyro_y = (float)gyro_y_raw / GYRO_SCALE_FACTOR;
    data->gyro_z = (float)gyro_z_raw / GYRO_SCALE_FACTOR;

    // 温度转换公式: Temperature = 36.53 + (TEMP_OUT / 340)
    data->temperature = 36.53f + ((float)temp_raw / 340.0f);

    return ESP_OK;
}

esp_err_t MPU6050::read_accel(float* accel_x, float* accel_y, float* accel_z) {
    if (!initialized_ || !accel_x || !accel_y || !accel_z) {
        return ESP_FAIL;
    }

    uint8_t raw_data[6];
    esp_err_t ret = read_regs(MPU6050_REG_ACCEL_XOUT_H, raw_data, 6);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read accelerometer data");
        return ret;
    }

    int16_t accel_x_raw = (int16_t)((raw_data[0] << 8) | raw_data[1]);
    int16_t accel_y_raw = (int16_t)((raw_data[2] << 8) | raw_data[3]);
    int16_t accel_z_raw = (int16_t)((raw_data[4] << 8) | raw_data[5]);

    *accel_x = (float)accel_x_raw / ACCEL_SCALE_FACTOR;
    *accel_y = (float)accel_y_raw / ACCEL_SCALE_FACTOR;
    *accel_z = (float)accel_z_raw / ACCEL_SCALE_FACTOR;

    return ESP_OK;
}

esp_err_t MPU6050::read_gyro(float* gyro_x, float* gyro_y, float* gyro_z) {
    if (!initialized_ || !gyro_x || !gyro_y || !gyro_z) {
        return ESP_FAIL;
    }

    uint8_t raw_data[6];
    esp_err_t ret = read_regs(MPU6050_REG_GYRO_XOUT_H, raw_data, 6);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read gyroscope data");
        return ret;
    }

    int16_t gyro_x_raw = (int16_t)((raw_data[0] << 8) | raw_data[1]);
    int16_t gyro_y_raw = (int16_t)((raw_data[2] << 8) | raw_data[3]);
    int16_t gyro_z_raw = (int16_t)((raw_data[4] << 8) | raw_data[5]);

    *gyro_x = (float)gyro_x_raw / GYRO_SCALE_FACTOR;
    *gyro_y = (float)gyro_y_raw / GYRO_SCALE_FACTOR;
    *gyro_z = (float)gyro_z_raw / GYRO_SCALE_FACTOR;

    return ESP_OK;
}

esp_err_t MPU6050::read_temperature(float* temperature) {
    if (!initialized_ || !temperature) {
        return ESP_FAIL;
    }

    uint8_t raw_data[2];
    esp_err_t ret = read_regs(MPU6050_REG_TEMP_OUT_H, raw_data, 2);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read temperature data");
        return ret;
    }

    int16_t temp_raw = (int16_t)((raw_data[0] << 8) | raw_data[1]);
    *temperature = 36.53f + ((float)temp_raw / 340.0f);

    return ESP_OK;
}

bool MPU6050::is_connected() {
    if (!i2c_device_) {
        return false;
    }

    uint8_t who_am_i;
    esp_err_t ret = read_reg(MPU6050_REG_WHO_AM_I, &who_am_i);
    return (ret == ESP_OK && (who_am_i == 0x68 || who_am_i == 0x72));
}

esp_err_t MPU6050::write_reg(uint8_t reg, uint8_t value) {
    if (!i2c_device_) {
        return ESP_FAIL;
    }

    uint8_t buffer[2] = {reg, value};
    return i2c_master_transmit(i2c_device_, buffer, 2, 100);
}

esp_err_t MPU6050::read_reg(uint8_t reg, uint8_t* value) {
    if (!i2c_device_ || !value) {
        return ESP_FAIL;
    }

    return i2c_master_transmit_receive(i2c_device_, &reg, 1, value, 1, 100);
}

esp_err_t MPU6050::read_regs(uint8_t reg, uint8_t* buffer, size_t length) {
    if (!i2c_device_ || !buffer || length == 0) {
        return ESP_FAIL;
    }

    return i2c_master_transmit_receive(i2c_device_, &reg, 1, buffer, length, 100);
}